"""ProjectHDF5Manager单元测试"""
import pytest
import tempfile
import numpy as np
from pathlib import Path
from datetime import date, datetime
from unittest.mock import Mock, patch

from pyahc.db.hdf5.manager import ProjectHDF5Manager
from pyahc.db.hdf5.exceptions import HDF5Error


class TestProjectHDF5Manager:
    """ProjectHDF5Manager测试类"""
    
    @pytest.fixture
    def temp_hdf5_file(self):
        """创建临时HDF5文件"""
        with tempfile.NamedTemporaryFile(suffix='.h5', delete=False) as f:
            temp_path = Path(f.name)
        yield temp_path
        # 清理
        if temp_path.exists():
            temp_path.unlink()
    
    @pytest.fixture
    def manager(self, temp_hdf5_file):
        """创建ProjectHDF5Manager实例"""
        return ProjectHDF5Manager(temp_hdf5_file, mode='w')
    
    @pytest.fixture
    def sample_metadata(self):
        """示例元数据"""
        return {
            'field_id': 'field_001',
            'crop_type': 'corn',
            'year': 2013,
            'latitude': 45.75,
            'longitude': -93.48,
            'created_at': datetime.now(),
            'config': {
                'simulation_start': date(2013, 5, 1),
                'simulation_end': date(2013, 10, 31),
                'enable_assimilation': False
            }
        }
    
    @pytest.fixture
    def mock_model(self):
        """模拟Model对象"""
        model = Mock()
        model.__class__.__name__ = 'Model'
        return model
    
    @pytest.fixture
    def mock_result(self):
        """模拟Result对象"""
        result = Mock()
        result.__class__.__name__ = 'Result'
        return result
    
    @pytest.fixture
    def sample_states(self):
        """示例状态变量"""
        return {
            'soil_moisture': np.array([0.25, 0.30, 0.35, 0.40]),
            'lai': 2.5,
            'biomass': 1500.0,
            'root_depth': 45.0
        }
    
    @pytest.fixture
    def sample_parameters(self):
        """示例参数变量"""
        return {
            'k_sat': 10.0,
            'field_capacity': 0.35,
            'wilting_point': 0.15
        }
    
    def test_init(self, temp_hdf5_file):
        """测试初始化"""
        manager = ProjectHDF5Manager(temp_hdf5_file)
        assert manager.filepath == temp_hdf5_file
        assert manager.mode == 'a'  # 默认模式
        assert manager._file is None
        assert not manager.is_open
    
    def test_context_manager(self, manager):
        """测试上下文管理器"""
        assert not manager.is_open
        
        with manager:
            assert manager.is_open
        
        assert not manager.is_open
    
    def test_open_close(self, manager):
        """测试文件打开和关闭"""
        assert not manager.is_open
        
        manager.open()
        assert manager.is_open
        
        manager.close()
        assert not manager.is_open
    
    def test_create_project(self, manager, sample_metadata):
        """测试创建项目"""
        project_name = 'corn_001-2013'
        
        with manager:
            manager.create_project(project_name, sample_metadata)
            
            # 验证项目存在
            assert manager.project_exists(project_name)
            
            # 验证项目在列表中
            projects = manager.list_projects()
            assert project_name in projects
            
            # 验证元数据
            loaded_metadata = manager.get_project_metadata(project_name)
            assert loaded_metadata['field_id'] == 'field_001'
            assert loaded_metadata['crop_type'] == 'corn'
            assert loaded_metadata['year'] == 2013
    
    def test_create_duplicate_project(self, manager, sample_metadata):
        """测试创建重复项目"""
        project_name = 'corn_001-2013'
        
        with manager:
            manager.create_project(project_name, sample_metadata)
            
            # 尝试创建重复项目应该抛出异常
            with pytest.raises(ValueError, match="already exists"):
                manager.create_project(project_name, sample_metadata)
    
    def test_project_not_exists(self, manager):
        """测试不存在的项目"""
        with manager:
            assert not manager.project_exists('nonexistent_project')
            
            with pytest.raises(ValueError, match="does not exist"):
                manager.get_project_metadata('nonexistent_project')
    
    def test_save_and_load_daily_data(self, manager, sample_metadata, mock_model, 
                                    mock_result, sample_states, sample_parameters):
        """测试保存和加载日数据"""
        project_name = 'corn_001-2013'
        test_date = date(2013, 5, 15)
        
        with manager:
            # 创建项目
            manager.create_project(project_name, sample_metadata)
            
            # 保存日数据
            manager.save_daily_data(project_name, test_date, mock_model, 
                                  mock_result, sample_states, sample_parameters)
            
            # 加载日数据
            loaded_data = manager.load_daily_data(project_name, test_date)
            
            # 验证数据
            assert loaded_data['model'] is not None
            assert loaded_data['result'] is not None
            assert 'soil_moisture' in loaded_data['states']
            assert 'lai' in loaded_data['states']
            assert 'k_sat' in loaded_data['parameters']
            
            # 验证数组数据
            np.testing.assert_array_equal(
                loaded_data['states']['soil_moisture'], 
                sample_states['soil_moisture']
            )
    
    def test_save_daily_data_without_parameters(self, manager, sample_metadata, 
                                              mock_model, mock_result, sample_states):
        """测试保存日数据（无参数）"""
        project_name = 'corn_001-2013'
        test_date = date(2013, 5, 15)
        
        with manager:
            manager.create_project(project_name, sample_metadata)
            manager.save_daily_data(project_name, test_date, mock_model, 
                                  mock_result, sample_states)
            
            loaded_data = manager.load_daily_data(project_name, test_date)
            assert loaded_data['parameters'] == {}
    
    def test_load_nonexistent_date(self, manager, sample_metadata):
        """测试加载不存在的日期"""
        project_name = 'corn_001-2013'
        test_date = date(2013, 5, 15)
        
        with manager:
            manager.create_project(project_name, sample_metadata)
            
            with pytest.raises(ValueError, match="not found"):
                manager.load_daily_data(project_name, test_date)
    
    def test_datetime_conversion(self, manager, sample_metadata, mock_model, 
                                mock_result, sample_states):
        """测试datetime到date的转换"""
        project_name = 'corn_001-2013'
        test_datetime = datetime(2013, 5, 15, 14, 30, 0)
        
        with manager:
            manager.create_project(project_name, sample_metadata)
            manager.save_daily_data(project_name, test_datetime, mock_model, 
                                  mock_result, sample_states)
            
            # 使用date加载应该成功
            test_date = test_datetime.date()
            loaded_data = manager.load_daily_data(project_name, test_date)
            assert loaded_data['model'] is not None
    
    def test_variable_units(self, manager):
        """测试变量单位获取"""
        units = manager._get_variable_units('soil_moisture')
        assert units == 'cm3/cm3'
        
        units = manager._get_variable_units('unknown_var')
        assert units == 'unknown'
    
    def test_metadata_with_nested_dict(self, manager):
        """测试嵌套字典元数据"""
        project_name = 'test_project'
        metadata = {
            'basic_info': {
                'field_id': 'field_001',
                'crop_type': 'corn'
            },
            'simulation_config': {
                'start_date': date(2013, 5, 1),
                'end_date': date(2013, 10, 31)
            }
        }
        
        with manager:
            manager.create_project(project_name, metadata)
            loaded_metadata = manager.get_project_metadata(project_name)
            
            assert 'basic_info' in loaded_metadata
            assert 'simulation_config' in loaded_metadata
            assert loaded_metadata['basic_info']['field_id'] == 'field_001'
    
    def test_error_handling_file_operations(self, temp_hdf5_file):
        """测试文件操作错误处理"""
        # 测试无效路径
        invalid_path = Path('/invalid/path/test.h5')
        manager = ProjectHDF5Manager(invalid_path)
        
        with pytest.raises(Exception):
            manager.open()
    
    def test_list_empty_projects(self, manager):
        """测试空项目列表"""
        with manager:
            projects = manager.list_projects()
            assert projects == []
    
    def test_multiple_projects(self, manager, sample_metadata):
        """测试多个项目"""
        projects = ['corn_001-2013', 'soybean_002-2014', 'wheat_003-2015']
        
        with manager:
            for project in projects:
                manager.create_project(project, sample_metadata)
            
            project_list = manager.list_projects()
            for project in projects:
                assert project in project_list
